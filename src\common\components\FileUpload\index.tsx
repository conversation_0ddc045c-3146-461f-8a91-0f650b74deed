/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-07-25 17:50:21
 * @LastEditTime: 2022-07-29 15:47:48
 * @LastEditors: zhangfengfei
 */
import { documentHost } from '@/services/api';
import { PlusOutlined } from '@ant-design/icons';
import { message, Modal, Upload } from 'antd';
import 'antd/es/slider/style';
import type { RcFile, UploadFile } from 'antd/lib/upload/interface';
import type { FC } from 'react';
import { useEffect, useState } from 'react';

interface ResponseFileDataItem {
  size: number;
  path: string;
  name: string;
  type: string;
  mtime: string;
}

// 上传接口返回数据
interface ApiResponseData {
  data: {
    fileId: string;
    name: string;
    accessUrl: string;
  };
}

type ResponseFileData = ResponseFileDataItem[];

type ImageType = 'jpg' | 'png' | 'jpeg';

// 文件信息类型定义
interface FileInfo {
  id: string;
  fileUrl: string;
  filePath?: string;
  fileName?: string;
  fileType?: string;
  fileSize?: number;
}

// 扩展 UploadFile 类型，添加自定义属性
interface CustomUploadFile extends UploadFile<ApiResponseData> {
  id?: string;
  fileUrl?: string;
  fileName?: string;
  fileType?: string;
  fileSize?: number;
  filePath?: string;
}

interface ImageUploadProps {
  className?: string;
  defaultValue?: FileInfo[];
  value?: FileInfo[];
  /** 上传列表变化的回调 */
  onChange?: (value: FileInfo[]) => void;
  /** 单文件大小  M */
  size?: number;
  /** 图片最大上传数量 */
  maxCount?: number;
  /** 允许上传的图片格式，默认 ['jpeg', 'jpg', 'png'] */
  imgTypes?: ImageType[];
  /** 是否支持批量上传 */
  multiple?: boolean;
  /** 上传地址 */
  action?: string;
  readonly?: boolean;
}
/**
 * @params 如被 FormItem 嵌套，value 和 onChange 无需再定义
 * @description  图片上传组件 有编辑和只读两个模式
 *
 */
const ImageUpload: FC<ImageUploadProps> = ({
  className,
  value,
  defaultValue,
  onChange: onValuesChange,
  maxCount = 1,
  imgTypes = ['jpeg', 'jpg', 'png'],
  multiple = false,
  readonly = false,
  size = 100,
  action = documentHost + '/file/custom/uploadFile',
}) => {
  const fileTypes = [...new Set(imgTypes)];
  const [fileList, setFileList] = useState<CustomUploadFile[]>([]);
  const [isInitialRender, setIsInitialRender] = useState(true);

  const [current, setCurrent] = useState<CustomUploadFile>();
  const [previewVisible, setPreviewVisible] = useState(false);

  // 上传前图片格式和大小校验
  const beforeUpload = (file: RcFile, FileList: RcFile[]) => {
    const typeFlag = fileTypes.map((type) => `image/${type}`).includes(file.type);
    if (!typeFlag) {
      // jpg 和 jpeg 是同一个格式
      message.error(`只允许上传${fileTypes.join(',')}格式的图片!`);
    }
    const sizeFlag = file.size / 1024 / 1024 < size;
    if (!sizeFlag) {
      message.error(`图片必须小于${size}M!`);
    }
    return (typeFlag && sizeFlag) || Upload.LIST_IGNORE;
  };

  // 上传change事件
  const onChange = ({
    file,
    fileList,
  }: {
    file: CustomUploadFile;
    fileList: CustomUploadFile[];
  }) => {
    const { status } = file;
    // 上传成功处理数据
    if (status === 'done') {
      setFileList(
        fileList.map((item) => {
          if (item.status === 'done') {
            if (item.fileUrl) {
              return item;
            } else if (item.response && item.response.data) {
              const file = item.response.data;
              return {
                ...item,
                id: file.fileId,
                fileName: file.name,
                fileUrl: file.accessUrl,
              };
            }
          }
          return item;
        }),
      );
      return;
    }
    if (status === 'error') {
      setFileList(fileList.filter((item) => item.status !== 'error'));
      message.error('图片上传失败！');
      return;
    }

    setFileList(fileList);
  };

  // 图片回显 - 只在初始化和 defaultValue 变化时执行
  useEffect(() => {
    if (defaultValue && defaultValue.length > 0) {
      const defaultFileList: CustomUploadFile[] = defaultValue.map((item) => ({
        uid: item.id,
        name: item.fileName || '',
        id: item.id,
        fileName: item.fileName,
        percent: 100,
        status: 'done',
        thumbUrl: item.fileUrl,
        fileUrl: item.fileUrl,
      }));
      setFileList(defaultFileList);
      setIsInitialRender(false);
    } else {
      // 如果 defaultValue 为空，也设置 isInitialRender 为 false，保证首次上传也能触发 onChange
      setIsInitialRender(false);
    }
  }, [defaultValue]);

  // 监听 fileList 变化，通知父组件
  useEffect(() => {
    // 跳过初始化渲染或 fileList 为空的情况
    if (isInitialRender) {
      return;
    }

    if (onValuesChange) {
      // 需过滤上传失败的图片
      const changedValue = fileList
        .filter((item) => item.fileUrl)
        .map((item) => ({
          id: item.id as string,
          fileUrl: item.fileUrl as string,
          fileName: item.fileName,
          fileType: item.fileType,
          fileSize: item.fileSize,
          filePath: item.filePath,
        }));
      onValuesChange(changedValue);
    }
  }, [fileList, isInitialRender, onValuesChange]);

  const accept = fileTypes.map((type) => `.${type}`).join(',');

  const uploadComponent = (
    <Upload
      className={className}
      fileList={fileList}
      listType="picture-card"
      accept={accept}
      maxCount={maxCount}
      beforeUpload={beforeUpload}
      onChange={onChange}
      onPreview={(file) => {
        setCurrent(file as CustomUploadFile);
        setPreviewVisible(true);
      }}
      showUploadList={{
        showRemoveIcon: !readonly,
      }}
      multiple={multiple}
      action={action}
      headers={{
        'system-type': 'e-commerce',
      }}
    >
      {fileList.length < maxCount && !readonly ? <PlusOutlined /> : null}
    </Upload>
  );

  return (
    <>
      {uploadComponent}
      <Modal
        visible={previewVisible}
        title={'预览'}
        footer={null}
        onCancel={() => setPreviewVisible(false)}
      >
        <img alt="图片预览" style={{ width: '100%' }} src={current?.thumbUrl} />
      </Modal>
    </>
  );
};

export default ImageUpload;
